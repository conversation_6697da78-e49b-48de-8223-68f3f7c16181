import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import Lazy = require("lazy");
import types = require("./types");
import SequenceAnalysis = types.SequenceAnalysis;
import {AnalysisContext} from "../chart-domain";
import {Building as BuildingDto, ServiceRecipient, SessionData} from "ecco-dto";
import {FinanceChargeDto, FinanceReceiptDto, OccupancyDto} from "ecco-dto";
import {ReferralSummaryDto} from "ecco-dto";
import {
    columnMap,
    dateColumn,
    hrefColumn,
    HrefData,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    numberFixedColumn,
    textColumn
} from "../controls/tableSupport";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {
    EntityType,
    entityUrl,
    referralSummaryColumns,
    serviceRecipientColumns
} from "../tables/predefined-table-representations";
import {fullAddress} from "ecco-dto";
import {buildingOnlyColumns} from "./buildingAnalysis";

// COPY from predefined-table-representations
const serviceRecipientHref: (srId: number) => HrefData = srId => {
    const hrefData: HrefData = {
        display: srId.toString(),
        url: entityUrl(srId.toString(), EntityType.ServiceRecipient) || undefined
    };
    return hrefData;
};

//*********************************
// Generalised functions

//*********************************
// Analysis: breakdown of finance

const financeChargeOnlyColumns = columnMap<FinanceChargeDto>(
    numberColumn("i-id", row => row.invoiceId),
    hrefColumn("sr-id", row => serviceRecipientHref(row.serviceRecipientId)),
    //() => window.open(getNewReferralSrIdDirectHref(data.referral.serviceRecipientId), '_blank'),
    textColumn("occ. type", row =>
        row.serviceRecipient?.prefix == "mv" ? "void (planned)" : "client"
    ),
    dateColumn("from", row =>
        row.chargeFrom ? EccoDateTime.parseIso8601(row.chargeFrom)?.toEccoDate() : null
    ),
    dateColumn("to", row =>
        row.chargeTo ? EccoDateTime.parseIso8601(row.chargeTo)?.toEccoDate() : null
    ),
    numberColumn("chargeNameId", row => row.chargeNameId),
    textColumn("charge type", (row, context) =>
        context.getSessionData().getListDefinitionEntryById(row.chargeNameId)?.getName()
    ),
    textColumn("reason", row => row.description),
    numberColumn("b-id", row => row.buildingId),
    numberFixedColumn("netAmount", 2, row => row.netAmount),
    numberFixedColumn("charge", 2, row => row.netAmount),

    numberFixedColumn("receiptTotal", 2, row => row.receiptTotal),
    numberFixedColumn("paid", 2, row => row.receiptTotal),

    numberFixedColumn("dueAmount", 2, row => row.dueAmount),
    numberFixedColumn("balance", 2, row => row.dueAmount)
);

const financeChargeToOccupiedColumns = joinNestedPathColumnMaps<
    FinanceChargeDto,
    ServiceRecipient | null
>("sr", row => row.serviceRecipient, serviceRecipientColumns);

const financeChargeToBuildingColumns = joinNestedPathColumnMaps<
    FinanceChargeDto,
    BuildingDto | null
>("b", row => row.building, buildingOnlyColumns);

const financeChargeWithOccupiedAndBuildingColumns = joinColumnMaps(
    financeChargeOnlyColumns,
    financeChargeToOccupiedColumns,
    financeChargeToBuildingColumns
);

export class FinanceChargeAnalysis extends SequenceAnalysis<FinanceChargeDto> {
    constructor(ctx: AnalysisContext, data: Sequence<FinanceChargeDto>) {
        super(ctx, data, (item: FinanceChargeDto) => `${item.lineUuid}`);
        this.recordRepresentation = {
            FinanceChargeOnly: financeChargeOnlyColumns,
            FinanceChargeWithOccupiedAndBuilding: financeChargeWithOccupiedAndBuildingColumns
        };
        this.derivativeAnalysers = {};
    }
}

const financeReceiptOnlyColumns = columnMap<FinanceReceiptDto>(
    numberColumn("rec-id", row => row.receiptId),
    numberColumn("sr-id", row => row.serviceRecipientId),
    dateColumn("received", row => EccoDate.parseIso8601(row.receivedDate)),
    textColumn("description", row => row.description),
    numberFixedColumn("amount", 2, row => row.amount)
);

const financeReceiptToReferralColumns = joinNestedPathColumnMaps<
    FinanceReceiptDto,
    ReferralSummaryDto | null
>("r", row => row.referralSummary, referralSummaryColumns);

const financeReceiptWithReferralColumns = joinColumnMaps(
    financeReceiptOnlyColumns,
    financeReceiptToReferralColumns
);

export class FinanceReceiptOnlyAnalysis extends SequenceAnalysis<FinanceReceiptDto> {
    constructor(ctx: AnalysisContext, data: Sequence<FinanceReceiptDto>) {
        super(ctx, data, (item: FinanceReceiptDto) => `${item.receiptId}`);
        this.recordRepresentation = {
            FinanceReceiptOnly: financeReceiptOnlyColumns,
            FinanceReceiptWithReferral: financeReceiptWithReferralColumns
        };
        this.derivativeAnalysers = {};
    }
}

export function financeCombined(
    charges: FinanceChargeDto[],
    receipts: FinanceReceiptDto[],
    sessionData: SessionData
): Sequence<FinanceChargeDto> {
    // collect the receipts not in any charge period
    const receiptsUsed: number[] = [];

    charges.forEach(c => {
        const receiptsForChargeLine = receipts.filter(r => {
            const chgFrom = EccoDate.parseIso8601FromDateTime(c.chargeFrom!)!;
            const chgTo = c.chargeTo ? EccoDate.parseIso8601FromDateTime(c.chargeTo) : null;
            const received = EccoDate.parseIso8601(r.receivedDate);
            const gtFrom = chgFrom.compare(received) < 0;
            const loeTo = c.chargeTo ? chgTo!.compare(received) >= 0 : true;
            const typeDefId = c.chargeNameId;
            const matchType = r.typeDefId == typeDefId;
            const receiptChargeMatch = gtFrom && loeTo && matchType;
            if (receiptChargeMatch) {
                receiptsUsed.push(r.receiptId);
            }
            return receiptChargeMatch;
        });

        c.receiptTotal = receiptsForChargeLine.reduce((prev, curr) => prev + curr.amount, 0);
        c.dueAmount = c.netAmount - c.receiptTotal;
    });

    receipts
        .filter(r => !receiptsUsed.includes(r.receiptId))
        .forEach(r => {
            const receiptOnlyCharge: FinanceChargeDto = {
                lineUuid: "",
                locked: false,
                rateCardId: 0,
                chargeNameId: r.typeDefId,
                reverseCharge: false,
                taxRate: 0,
                type: null, // this will be an invoice line charge, not the receipt typeDefId
                /*type: r.typeDefId != null
                    ? sessionData.getListDefinitionEntryById(r.typeDefId)!.getName()
                    : null,*/
                invoiceId: 0,
                serviceRecipientId: r.serviceRecipientId,
                serviceRecipient: r.referralSummary,
                description: r.description,
                buildingId: 0,
                netAmount: 0,
                receiptTotal: r.amount,
                dueAmount: -r.amount,
                links: []
            };
            charges.push(receiptOnlyCharge);
        });

    // NB we can have receipts not relevant to any period - but perhaps the report needs a discussion
    //const remainingReceipts = [];
    return Lazy(charges);
}

//*********************************
// Analysis: breakdown of occupancy

// see AddressHistory
const occupancyOnlyColumns = columnMap(
    numberColumn<OccupancyDto>("id", row => row.id),
    numberColumn<OccupancyDto>("o-id", row => row.addressId),
    numberColumn<OccupancyDto>("sr-id", row => row.serviceRecipientId),
    dateColumn<OccupancyDto>("valid from", row => EccoDate.parseIso8601FromDateTime(row.validFrom)),
    dateColumn<OccupancyDto>("valid to", row => EccoDate.parseIso8601FromDateTime(row.validTo!)),
    textColumn<OccupancyDto>("full address", row => fullAddress(row.address))
);

const occupancyToReferralSummaryColumns = joinNestedPathColumnMaps<
    OccupancyDto,
    ReferralSummaryDto
>("r", row => row.referralSummary, referralSummaryColumns);
const occupancyToBuildingColumns = joinNestedPathColumnMaps<OccupancyDto, BuildingDto>(
    "b",
    row => row.building,
    buildingOnlyColumns
);

const occupancyWithColumns = joinColumnMaps(
    occupancyOnlyColumns,
    occupancyToReferralSummaryColumns,
    occupancyToBuildingColumns
);

export class OccupancyAnalysis extends SequenceAnalysis<OccupancyDto> {
    constructor(ctx: AnalysisContext, data: Sequence<OccupancyDto>) {
        super(ctx, data, (item: OccupancyDto) => item.id.toString());
        this.recordRepresentation = {
            OccupancyOnly: occupancyOnlyColumns,
            OccupancyWith: occupancyWithColumns
        };
        this.derivativeAnalysers = {};
    }
}
