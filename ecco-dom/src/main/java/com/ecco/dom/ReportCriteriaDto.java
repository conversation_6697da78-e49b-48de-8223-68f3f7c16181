package com.ecco.dom;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.ISODateTimeFormat;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/** We want to be able to pass search criteria deep into the stack, so this has been pushed in to here from
 * the web API - where it is used.
 */
@Data
public class ReportCriteriaDto implements Serializable {

    private static final long serialVersionUID = 1L;


    public static Builder builder() {
        return new Builder(new ReportCriteriaDto());
    }

    public static class Builder {

        private final ReportCriteriaDto criteria;

        public Builder(ReportCriteriaDto dto) {
            this.criteria = dto;
        }

        public ReportCriteriaDto build() {
            return criteria;
        }

        public Builder withReferralStatus(ReferralStatusName status) {
            criteria.setReferralStatus(status.getName());
            return this;
        }

        public Builder withNewReferralsOnly(Boolean newReferralsOnly) {
            criteria.setNewReferralsOnly(newReferralsOnly);
            return this;
        }

        public Builder withServiceId(Long serviceId) {
            criteria.setServiceId(serviceId);
            return this;
        }

        public Builder withProjectId(Long projectId) {
            criteria.setProjectId(projectId);
            return this;
        }

        public Builder withJodaFrom(DateTime from) {
            criteria.setFrom(from.toLocalDate().toString(ISODateTimeFormat.date()));
            return this;
        }

        public Builder withJodaTo(DateTime to) {
            criteria.setFrom(to.toLocalDate().toString(ISODateTimeFormat.date()));
            return this;
        }
    }

    /**
     * GENERIC CRITERIA (non-referral)
     */

    String from; // TODO: this is ISO-8601 date yyyy-MM-dd - TODO make it a LocalDate

    /** inclusive to date (see PredicateSupport#applyLocalDateRange) */
    String to;   // TODO: make LocalDate

    /**
     * The property that the report dates are applicable for.
     */
    String selectionPropertyPath;

    /**
     * The client-side list of fetchRelatedEntities (extra data to be loaded).
     * eg riskWork, or 'childRecipientIds' for /reports/referrals/.
     */
    Set<String> optionalData;

    /**
     * Status of the entity - similar to referralStatus, except meaningful for the entity
     * eg liveAsEnd for a review, means get all the reviews latest status (don't use dates)
     */
    String entityStatus;

    /**
     * FILTERS on the criteria.
     * NB these perhaps shouldn't be hard-coded, and could be:
     *  - [operand, filterPropertyPath, filterPropertyValue]
     */
    /** Filter the report by a particular srId */
    String serviceRecipientFilter;

    /** Filter the report by a particular userId */
    Long userId;
    String username;

    /** Filter the evidence group name to retrieve for questionnaires involved in this report */
    String questionnaireEvidenceGroup;
    List<String> questionnaireEvidenceGroupArr;

    /** Filter the command name to retrieve for audits involved in this report */
    List<String> commandNameArr;

    /** The evidence group name to retrieve for support involved in this report (eg supportStaffNotes or managerNotes).
     * If not provided, then 'needs' is assumed.
     */
    String supportEvidenceGroup;

    String taskDefName;

    String customFormEvidenceGroup;

    /** Filter the group support */
    Integer venueId;
    Integer activityTypeId;
    String groupPageType;
    Long parentId;
    /** Filter the group support */

    /** Filter for occupancy, and finance report for bldg */
    Integer[] buildingIds;
    /** Filter for occupancy */

    /**
     * REFERRAL CRITERIA
     */

    Long serviceId;
    Long projectId;
    Integer serviceCategorisationId;
    Integer companyId;
    Integer supportWorkerId;
    Integer clientGroupId;
    Integer serviceGroupId;

    /** If set, filters for is or is not a child referral. null means get children and parents */
    Boolean isChild;

    /** The item selected e.g. the id of the ward for a single ward within a district.
     * Refers to Referral.srcGeographicArea */
    Integer geographicAreaIdSelected;

    /** For a match on all referrals whose srcGeographicArea's parent matches this id e.g. the id of the district to get
     *  all where geographicArea is a ward within that district */
    List<Integer> geographicAreaIds;

    /**
     * Referral status
     * See feature-config/domain.ts for the presented list client-side
     * and ReportCriteriaDto.ts for this matching dto
     * and ReferralStatusName for the status that can be used
     */
    String referralStatus;

    /** To differentiate between the meaning of the status - eg:
     * 1) get referrals who were 'live' during the period (which could bring back the entire database)
     * OR with this setting set:
     * 2) get referrals who were 'live' during the period who were also received during the period */
    Boolean newReferralsOnly;

    /**
     * Includes the primary referrals - default false
     * So reports (eg export reports) can include the related referrals to a primary referral
     */
    Boolean includeRelated = false;

    /**
     * Convert the string into the LocalDate we expect from ReportCriteriaDto.ts
     */
    @JsonIgnore // see also JsonIgnoreProperties
    public LocalDate getFromDate() {
        return from == null ? null : ISODateTimeFormat.date().parseLocalDate(from);
    }

    @JsonIgnore
    public LocalDate getToDate() {
        return to == null ? null : ISODateTimeFormat.date().parseLocalDate(to);
    }

}
